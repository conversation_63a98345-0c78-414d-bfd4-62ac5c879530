<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tools Dashboard - Latest AI Tools & Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- <PERSON><PERSON>sent Banner -->
    <div id="cookieConsent" class="cookie-consent">
        <div class="cookie-content">
            <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
            <div class="cookie-actions">
                <button id="acceptCookies" class="btn-accept">Accept</button>
                <a href="https://jermesa.com/privacy-policy/" target="_blank" class="privacy-link">Privacy Policy</a>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-icon">
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <rect width="32" height="32" rx="8" fill="#6366f1"/>
                        <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" fill="white"/>
                        <circle cx="20" cy="20" r="3" fill="#fbbf24"/>
                    </svg>
                </div>
                <h1>AI Tools Dashboard</h1>
            </div>
            
            <div class="controls-section">
                <!-- AI Provider Selection -->
                <div class="control-group">
                    <label for="aiProvider">AI Provider:</label>
                    <select id="aiProvider" class="control-select">
                        <option value="">Select Provider</option>
                        <option value="openrouter">OpenRouter</option>
                        <option value="gemini">Google Gemini</option>
                        <option value="deepseek">DeepSeek</option>
                    </select>
                </div>

                <!-- API Key Input -->
                <div class="control-group">
                    <label for="apiKey">API Key:</label>
                    <input type="password" id="apiKey" class="control-input" placeholder="Enter your API key">
                </div>

                <!-- Model Selection -->
                <div class="control-group">
                    <label for="modelSelect">Model:</label>
                    <select id="modelSelect" class="control-select" disabled>
                        <option value="">Select Model</option>
                    </select>
                </div>

                <!-- Time Frame -->
                <div class="control-group">
                    <label for="timeFrame">Time Frame:</label>
                    <select id="timeFrame" class="control-select">
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                        <option value="year">This Year</option>
                        <option value="all">All Time</option>
                    </select>
                </div>

                <!-- Search Button -->
                <button id="searchBtn" class="search-btn" disabled>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M7 12A5 5 0 1 0 7 2a5 5 0 0 0 0 10zM13 13l-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    Get Tools
                </button>
            </div>
        </div>

        <!-- Search and Filter Bar -->
        <div class="search-filter-bar">
            <div class="search-container">
                <svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M7 12A5 5 0 1 0 7 2a5 5 0 0 0 0 10zM13 13l-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <input type="text" id="searchInput" placeholder="Search tools..." class="search-input">
            </div>
            
            <div class="filter-tags" id="filterTags">
                <!-- Dynamic filter tags will be added here -->
            </div>

            <div class="settings-controls">
                <button id="settingsBtn" class="settings-btn">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M12.7 8a4.7 4.7 0 0 0-.1-.9l1.4-.8a.5.5 0 0 0 .2-.7l-1.3-2.2a.5.5 0 0 0-.7-.2l-1.4.8a4.7 4.7 0 0 0-.8-.4V2.5a.5.5 0 0 0-.5-.5H6.5a.5.5 0 0 0-.5.5v1.6a4.7 4.7 0 0 0-.8.4l-1.4-.8a.5.5 0 0 0-.7.2L1.8 6.1a.5.5 0 0 0 .2.7l1.4.8a4.7 4.7 0 0 0 0 1.8l-1.4.8a.5.5 0 0 0-.2.7l1.3 2.2a.5.5 0 0 0 .7.2l1.4-.8a4.7 4.7 0 0 0 .8.4v1.6a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-1.6a4.7 4.7 0 0 0 .8-.4l1.4.8a.5.5 0 0 0 .7-.2l1.3-2.2a.5.5 0 0 0-.2-.7l-1.4-.8z" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                    Settings
                </button>
            </div>
        </div>
    </header>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Dashboard Settings</h3>
                <button id="closeSettings" class="close-btn">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label>Visible Columns:</label>
                    <div class="column-toggles" id="columnToggles">
                        <!-- Dynamic column toggles will be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="loading-indicator">
        <div class="loading-spinner"></div>
        <p>Searching for the latest AI tools...</p>
    </div>

    <!-- Main Dashboard -->
    <main class="dashboard" id="dashboard">
        <!-- Dynamic columns will be added here -->
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="attribution">
                <p>Font: <a href="https://fonts.google.com/specimen/Inter" target="_blank">Inter</a> by Rasmus Andersson (Open Font License)</p>
                <p>Powered by <a href="https://www.jermesa.com" target="_blank">Jermesa Studio</a></p>
                <p><a href="https://jermesa.com/privacy-policy/" target="_blank">Privacy Policy</a></p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
