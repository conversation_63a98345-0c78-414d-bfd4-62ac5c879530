<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Response Parsing</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        textarea { width: 100%; height: 200px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>AI Response Parsing Test</h1>
    
    <div class="test-section">
        <h3>Test AI Response Parsing</h3>
        <textarea id="testResponse" placeholder="Paste AI response here to test parsing...">
Here are some current AI tools:

```json
[
  {
    "name": "Claude 3.5 Sonnet",
    "description": "Anthropic's latest AI model with enhanced reasoning capabilities",
    "url": "https://claude.ai",
    "category": "llms",
    "tags": ["anthropic", "reasoning", "latest"]
  },
  {
    "name": "GPT-4 Turbo",
    "description": "OpenAI's most advanced model with improved performance",
    "url": "https://openai.com/gpt-4",
    "category": "llms",
    "tags": ["openai", "gpt-4", "advanced"]
  }
]
```

These tools represent the current state of AI technology.
        </textarea>
        <button onclick="testParsing()">Test Parsing</button>
        <div id="parseResult" class="result"></div>
    </div>

    <script>
        // Copy the parseAIResponse method from the main script for testing
        function parseAIResponse(response) {
            console.log('Raw AI Response:', response);
            
            try {
                // Clean the response first
                let cleanedResponse = response.trim();
                
                // Try multiple extraction methods
                let jsonData = null;
                
                // Method 1: Direct JSON parsing if response starts with [
                if (cleanedResponse.startsWith('[')) {
                    try {
                        jsonData = JSON.parse(cleanedResponse);
                    } catch (e) {
                        console.log('Direct parsing failed, trying extraction methods...');
                    }
                }
                
                // Method 2: Extract JSON array from markdown code blocks
                if (!jsonData) {
                    const codeBlockMatch = cleanedResponse.match(/```(?:json)?\s*(\[[\s\S]*?\])\s*```/i);
                    if (codeBlockMatch) {
                        try {
                            jsonData = JSON.parse(codeBlockMatch[1]);
                        } catch (e) {
                            console.log('Code block extraction failed...');
                        }
                    }
                }
                
                // Method 3: Extract JSON array from anywhere in the text
                if (!jsonData) {
                    const jsonMatch = cleanedResponse.match(/\[[\s\S]*?\]/);
                    if (jsonMatch) {
                        try {
                            // Clean up common JSON issues
                            let jsonString = jsonMatch[0];
                            
                            // Fix common issues
                            jsonString = jsonString
                                .replace(/,\s*}/g, '}')  // Remove trailing commas in objects
                                .replace(/,\s*]/g, ']')  // Remove trailing commas in arrays
                                .replace(/'/g, '"')      // Replace single quotes with double quotes
                                .replace(/(\w+):/g, '"$1":') // Quote unquoted keys
                                .replace(/:\s*([^",\[\]{}]+)(?=\s*[,}\]])/g, ': "$1"'); // Quote unquoted string values
                            
                            jsonData = JSON.parse(jsonString);
                        } catch (e) {
                            console.log('JSON extraction and cleaning failed:', e);
                        }
                    }
                }
                
                // Validate and clean the parsed data
                if (jsonData && Array.isArray(jsonData)) {
                    jsonData = jsonData.filter(item => 
                        item && 
                        typeof item === 'object' && 
                        item.name && 
                        item.description && 
                        item.category
                    ).map(item => ({
                        name: String(item.name).trim(),
                        description: String(item.description).trim().substring(0, 200),
                        url: item.url || '#',
                        category: String(item.category).toLowerCase().trim(),
                        tags: Array.isArray(item.tags) ? item.tags.map(tag => String(tag).trim()) : []
                    }));
                    
                    if (jsonData.length > 0) {
                        console.log(`Successfully parsed ${jsonData.length} tools`);
                        return jsonData;
                    }
                }
                
                return null;
                
            } catch (error) {
                console.error('Error parsing AI response:', error);
                return null;
            }
        }

        function testParsing() {
            const response = document.getElementById('testResponse').value;
            const resultDiv = document.getElementById('parseResult');
            
            try {
                const parsed = parseAIResponse(response);
                if (parsed && parsed.length > 0) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ Parsing Successful!</h4>
                        <p>Found ${parsed.length} tools:</p>
                        <pre>${JSON.stringify(parsed, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ Parsing Failed</h4>
                        <p>Could not extract valid tool data from the response.</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ Error</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        // Test on page load
        window.onload = function() {
            testParsing();
        };
    </script>
</body>
</html>
