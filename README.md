# AI Tools Dashboard

A beautiful, modern web application for discovering and organizing the latest AI tools, LLMs, coding assistants, and AI services. Built with a minimalistic light theme and powered by AI for intelligent web searching.

## Features

### 🎨 Modern Design
- Clean, minimalistic light theme
- Responsive design that works on all devices
- Custom SVG icons (no external dependencies)
- Beautiful typography using Inter font (Open Font License)

### 🤖 AI-Powered Search
- Integration with multiple AI providers:
  - **OpenRouter** - Access to multiple AI models
  - **Google Gemini** - Google's advanced AI models
  - **DeepSeek** - Efficient AI models
- Live model selection from each provider
- Intelligent web searching for AI tools and services

### 📊 Dashboard Columns
- **Latest Tools** - Newest AI tools and services
- **Most Visited** - Popular and trending AI tools
- **LLMs** - Large Language Models and AI models
- **AI Tools** - General AI tools and applications
- **AI News** - Latest AI news and updates
- **API Providers** - AI API services and providers
- **SaaS Tools** - AI-powered SaaS applications
- **Coding Tools** - AI coding assistants and development tools

### 🔧 Advanced Features
- **Individual Column Scrolling** - Each column scrolls independently
- **Customizable Columns** - Show/hide columns via settings
- **Time Frame Selection** - Search for tools from this week, month, year, or all time
- **Search & Filter** - Real-time search and tag-based filtering
- **Responsive Layout** - Adapts to different screen sizes

### 🍪 Privacy & Compliance
- Functional cookie consent banner
- Privacy policy integration
- Local storage for user preferences
- No external tracking

## Getting Started

### Prerequisites
- A modern web browser
- API key from one of the supported providers:
  - [OpenRouter](https://openrouter.ai/) - For access to multiple AI models
  - [Google AI Studio](https://makersuite.google.com/) - For Gemini models
  - [DeepSeek](https://platform.deepseek.com/) - For DeepSeek models

### Installation

1. **Clone or download** this repository to your local machine

2. **Start a local server** (choose one method):
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Python 2
   python -m SimpleHTTPServer 8000
   
   # Using Node.js (if you have it installed)
   npx serve .
   
   # Using PHP (if you have it installed)
   php -S localhost:8000
   ```

3. **Open your browser** and navigate to:
   ```
   http://localhost:8000
   ```

### Usage

1. **Select AI Provider**: Choose from OpenRouter, Google Gemini, or DeepSeek
2. **Enter API Key**: Input your API key for the selected provider
3. **Choose Model**: Select from available models (loaded automatically)
4. **Set Time Frame**: Choose whether to search for tools from this week, month, year, or all time
5. **Click "Get Tools"**: The AI will search the web and populate the dashboard
6. **Explore Results**: Browse through different columns, search, and filter results
7. **Customize View**: Use the settings button to show/hide columns

### Settings

Click the settings button (⚙️) to:
- **Show/Hide Columns**: Customize which columns are visible
- **Save Preferences**: Settings are automatically saved to local storage

### Search & Filter

- **Search Bar**: Type to search across tool names, descriptions, and tags
- **Filter Tags**: Click on tags to filter results by category
- **Column Scrolling**: Each column scrolls independently for easy browsing

## Technical Details

### Architecture
- **Frontend**: Pure HTML5, CSS3, and JavaScript (ES6+)
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Icons**: Custom SVG icons (no external dependencies)
- **Fonts**: Inter font from Google Fonts (Open Font License)
- **Storage**: Local Storage for user preferences and settings

### API Integration
The application integrates with three AI providers:

#### OpenRouter
- **Endpoint**: `https://openrouter.ai/api/v1/`
- **Models**: Dynamic loading from `/models` endpoint
- **Chat**: `/chat/completions` endpoint

#### Google Gemini
- **Endpoint**: `https://generativelanguage.googleapis.com/v1beta/`
- **Models**: Predefined (gemini-1.5-pro, gemini-1.5-flash, gemini-pro)
- **Chat**: `/models/{model}:generateContent` endpoint

#### DeepSeek
- **Endpoint**: `https://api.deepseek.com/v1/`
- **Models**: Dynamic loading from `/models` endpoint
- **Chat**: `/chat/completions` endpoint

### File Structure
```
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality
└── README.md           # This documentation
```

## Browser Compatibility

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## License & Attribution

### Fonts
- **Inter** by Rasmus Andersson - [Open Font License](https://fonts.google.com/specimen/Inter)

### Powered By
- **Jermesa Studio** - [www.jermesa.com](https://www.jermesa.com)

### Privacy
- [Privacy Policy](https://jermesa.com/privacy-policy/)

## Contributing

This is an open-source project. Feel free to contribute by:
- Reporting bugs
- Suggesting new features
- Improving documentation
- Adding new AI provider integrations

## Support

For support or questions, please visit [Jermesa Studio](https://www.jermesa.com).

---

**Note**: This application requires an active internet connection and valid API keys from supported AI providers to function properly. The AI search functionality depends on the selected AI model's ability to search and parse web content.
