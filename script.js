// AI Tools Dashboard - Main JavaScript File
class AIToolsDashboard {
    constructor() {
        this.apiKey = '';
        this.selectedProvider = '';
        this.selectedModel = '';
        this.tools = [];
        this.filteredTools = [];
        this.columns = [
            { id: 'latest', title: 'Latest Tools', visible: true },
            { id: 'popular', title: 'Most Visited', visible: true },
            { id: 'llms', title: 'LLMs', visible: true },
            { id: 'tools', title: 'AI Tools', visible: true },
            { id: 'news', title: 'AI News', visible: true },
            { id: 'apis', title: 'API Providers', visible: true },
            { id: 'saas', title: 'SaaS Tools', visible: true },
            { id: 'coding', title: 'Coding Tools', visible: true }
        ];
        
        this.providerConfigs = {
            openrouter: {
                name: 'OpenRouter',
                baseUrl: 'https://openrouter.ai/api/v1',
                modelsEndpoint: '/models',
                chatEndpoint: '/chat/completions'
            },
            gemini: {
                name: '<PERSON> Gemini',
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                modelsEndpoint: '/models',
                chatEndpoint: '/models/{model}:generateContent'
            },
            deepseek: {
                name: 'DeepSeek',
                baseUrl: 'https://api.deepseek.com/v1',
                modelsEndpoint: '/models',
                chatEndpoint: '/chat/completions'
            }
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCookieConsent();
        this.loadSettings();
        this.renderColumns();
        this.setupFilterTags();
    }

    setupEventListeners() {
        // Cookie consent
        document.getElementById('acceptCookies').addEventListener('click', () => {
            this.acceptCookies();
        });

        // AI Provider selection
        document.getElementById('aiProvider').addEventListener('change', (e) => {
            this.selectedProvider = e.target.value;
            this.onProviderChange();
        });

        // API Key input
        document.getElementById('apiKey').addEventListener('input', (e) => {
            this.apiKey = e.target.value;
            this.validateInputs();
        });

        // Model selection
        document.getElementById('modelSelect').addEventListener('change', (e) => {
            this.selectedModel = e.target.value;
            this.validateInputs();
        });

        // Search button
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.searchTools();
        });

        // Search input
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.filterTools(e.target.value);
        });

        // Settings button
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.showSettings();
        });

        // Close settings
        document.getElementById('closeSettings').addEventListener('click', () => {
            this.hideSettings();
        });

        // Modal backdrop click
        document.getElementById('settingsModal').addEventListener('click', (e) => {
            if (e.target.id === 'settingsModal') {
                this.hideSettings();
            }
        });
    }

    initializeCookieConsent() {
        const consent = localStorage.getItem('cookieConsent');
        if (!consent) {
            setTimeout(() => {
                document.getElementById('cookieConsent').classList.add('show');
            }, 1000);
        }
    }

    acceptCookies() {
        localStorage.setItem('cookieConsent', 'accepted');
        document.getElementById('cookieConsent').classList.remove('show');
    }

    loadSettings() {
        const settings = localStorage.getItem('dashboardSettings');
        if (settings) {
            const parsed = JSON.parse(settings);
            this.columns = parsed.columns || this.columns;
        }
    }

    saveSettings() {
        const settings = {
            columns: this.columns
        };
        localStorage.setItem('dashboardSettings', JSON.stringify(settings));
    }

    async onProviderChange() {
        if (!this.selectedProvider) {
            document.getElementById('modelSelect').disabled = true;
            document.getElementById('modelSelect').innerHTML = '<option value="">Select Model</option>';
            return;
        }

        try {
            await this.loadModels();
        } catch (error) {
            console.error('Error loading models:', error);
            this.showNotification('Error loading models. Please check your API key.', 'error');
        }
    }

    async loadModels() {
        if (!this.apiKey || !this.selectedProvider) return;

        const config = this.providerConfigs[this.selectedProvider];
        const modelSelect = document.getElementById('modelSelect');
        
        modelSelect.innerHTML = '<option value="">Loading models...</option>';
        modelSelect.disabled = true;

        try {
            let models = [];
            
            if (this.selectedProvider === 'openrouter') {
                models = await this.fetchOpenRouterModels();
            } else if (this.selectedProvider === 'gemini') {
                models = await this.fetchGeminiModels();
            } else if (this.selectedProvider === 'deepseek') {
                models = await this.fetchDeepSeekModels();
            }

            modelSelect.innerHTML = '<option value="">Select Model</option>';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });
            
            modelSelect.disabled = false;
        } catch (error) {
            modelSelect.innerHTML = '<option value="">Error loading models</option>';
            throw error;
        }
    }

    async fetchOpenRouterModels() {
        const response = await fetch('https://openrouter.ai/api/v1/models', {
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) throw new Error('Failed to fetch models');
        
        const data = await response.json();
        return data.data.map(model => ({
            id: model.id,
            name: model.name || model.id
        }));
    }

    async fetchGeminiModels() {
        // Gemini models are predefined
        return [
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
            { id: 'gemini-pro', name: 'Gemini Pro' }
        ];
    }

    async fetchDeepSeekModels() {
        const response = await fetch('https://api.deepseek.com/v1/models', {
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) throw new Error('Failed to fetch models');
        
        const data = await response.json();
        return data.data.map(model => ({
            id: model.id,
            name: model.id
        }));
    }

    validateInputs() {
        const searchBtn = document.getElementById('searchBtn');
        const isValid = this.apiKey && this.selectedProvider && this.selectedModel;
        searchBtn.disabled = !isValid;
    }

    async searchTools() {
        if (!this.apiKey || !this.selectedProvider || !this.selectedModel) {
            this.showNotification('Please select AI provider, enter API key, and choose a model.', 'error');
            return;
        }

        const timeFrame = document.getElementById('timeFrame').value;
        this.showLoading(true);

        try {
            const searchPrompt = this.generateSearchPrompt(timeFrame);
            const results = await this.queryAI(searchPrompt);
            this.tools = this.parseAIResponse(results);
            this.filteredTools = [...this.tools];
            this.renderToolsInColumns();
            this.showNotification(`Found ${this.tools.length} AI tools!`, 'success');
        } catch (error) {
            console.error('Search error:', error);
            this.showNotification('Error searching for tools. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    generateSearchPrompt(timeFrame) {
        const timeFrameText = {
            'week': 'this week',
            'month': 'this month', 
            'year': 'this year',
            'all': 'all time'
        }[timeFrame] || 'recent';

        return `Please search the web and provide a comprehensive list of AI tools, services, and resources from ${timeFrameText}. 
        
        I need the results categorized into these categories:
        1. Latest Tools - Newest AI tools and services
        2. Most Visited - Popular and trending AI tools
        3. LLMs - Large Language Models and AI models
        4. AI Tools - General AI tools and applications
        5. AI News - Latest AI news and updates
        6. API Providers - AI API services and providers
        7. SaaS Tools - AI-powered SaaS applications
        8. Coding Tools - AI coding assistants and development tools

        For each tool, please provide:
        - Name
        - Description (brief, 1-2 sentences)
        - URL/Link
        - Category
        - Tags (relevant keywords)

        Please format the response as a JSON array with objects containing: name, description, url, category, tags.
        Focus on finding real, current tools and services. Include both free and paid options.`;
    }

    async queryAI(prompt) {
        const config = this.providerConfigs[this.selectedProvider];
        
        if (this.selectedProvider === 'openrouter') {
            return await this.queryOpenRouter(prompt);
        } else if (this.selectedProvider === 'gemini') {
            return await this.queryGemini(prompt);
        } else if (this.selectedProvider === 'deepseek') {
            return await this.queryDeepSeek(prompt);
        }
    }

    async queryOpenRouter(prompt) {
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': window.location.origin,
                'X-Title': 'AI Tools Dashboard'
            },
            body: JSON.stringify({
                model: this.selectedModel,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 4000,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    async queryGemini(prompt) {
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${this.selectedModel}:generateContent?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    maxOutputTokens: 4000,
                    temperature: 0.7
                }
            })
        });

        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status}`);
        }

        const data = await response.json();
        return data.candidates[0].content.parts[0].text;
    }

    async queryDeepSeek(prompt) {
        const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: this.selectedModel,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 4000,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            throw new Error(`DeepSeek API error: ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    parseAIResponse(response) {
        try {
            // Try to extract JSON from the response
            const jsonMatch = response.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // If no JSON found, create sample data for demonstration
            return this.generateSampleData();
        } catch (error) {
            console.error('Error parsing AI response:', error);
            return this.generateSampleData();
        }
    }

    generateSampleData() {
        // Sample data for demonstration when AI response parsing fails
        return [
            {
                name: "ChatGPT",
                description: "Advanced conversational AI by OpenAI for various tasks and assistance.",
                url: "https://chat.openai.com",
                category: "llms",
                tags: ["conversational", "openai", "gpt"]
            },
            {
                name: "Claude",
                description: "AI assistant by Anthropic focused on helpful, harmless, and honest interactions.",
                url: "https://claude.ai",
                category: "llms", 
                tags: ["anthropic", "assistant", "claude"]
            },
            {
                name: "GitHub Copilot",
                description: "AI-powered code completion and programming assistant for developers.",
                url: "https://github.com/features/copilot",
                category: "coding",
                tags: ["github", "coding", "autocomplete"]
            },
            {
                name: "Midjourney",
                description: "AI image generation tool for creating artistic and creative visuals.",
                url: "https://midjourney.com",
                category: "tools",
                tags: ["image", "generation", "art"]
            },
            {
                name: "Notion AI",
                description: "AI-powered writing and productivity features integrated into Notion workspace.",
                url: "https://notion.so/product/ai",
                category: "saas",
                tags: ["productivity", "writing", "workspace"]
            }
        ];
    }

    renderColumns() {
        const dashboard = document.getElementById('dashboard');
        dashboard.innerHTML = '';

        this.columns.filter(col => col.visible).forEach(column => {
            const columnElement = this.createColumnElement(column);
            dashboard.appendChild(columnElement);
        });
    }

    createColumnElement(column) {
        const columnDiv = document.createElement('div');
        columnDiv.className = 'dashboard-column';
        columnDiv.dataset.columnId = column.id;

        const header = document.createElement('div');
        header.className = 'column-header';
        
        const title = document.createElement('div');
        title.className = 'column-title';
        title.innerHTML = `
            ${column.title}
            <span class="column-count">0</span>
        `;
        
        header.appendChild(title);
        
        const content = document.createElement('div');
        content.className = 'column-content';
        
        columnDiv.appendChild(header);
        columnDiv.appendChild(content);
        
        return columnDiv;
    }

    renderToolsInColumns() {
        // Clear all columns
        document.querySelectorAll('.column-content').forEach(content => {
            content.innerHTML = '';
        });

        // Group tools by category
        const toolsByCategory = {};
        this.filteredTools.forEach(tool => {
            const category = tool.category || 'tools';
            if (!toolsByCategory[category]) {
                toolsByCategory[category] = [];
            }
            toolsByCategory[category].push(tool);
        });

        // Render tools in their respective columns
        Object.entries(toolsByCategory).forEach(([category, tools]) => {
            const column = document.querySelector(`[data-column-id="${category}"] .column-content`);
            const countElement = document.querySelector(`[data-column-id="${category}"] .column-count`);
            
            if (column && countElement) {
                countElement.textContent = tools.length;
                tools.forEach(tool => {
                    const toolElement = this.createToolElement(tool);
                    column.appendChild(toolElement);
                });
            }
        });
    }

    createToolElement(tool) {
        const toolDiv = document.createElement('div');
        toolDiv.className = 'tool-item';
        
        const thumbnail = tool.name.charAt(0).toUpperCase();
        
        toolDiv.innerHTML = `
            <div class="tool-header">
                <div class="tool-thumbnail">${thumbnail}</div>
                <div class="tool-info">
                    <div class="tool-name">${tool.name}</div>
                    <div class="tool-description">${tool.description}</div>
                </div>
            </div>
            <a href="${tool.url}" target="_blank" class="tool-link">Visit Tool →</a>
            <div class="tool-tags">
                ${(tool.tags || []).map(tag => `<span class="tool-tag">${tag}</span>`).join('')}
            </div>
        `;
        
        return toolDiv;
    }

    filterTools(searchTerm) {
        if (!searchTerm) {
            this.filteredTools = [...this.tools];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredTools = this.tools.filter(tool => 
                tool.name.toLowerCase().includes(term) ||
                tool.description.toLowerCase().includes(term) ||
                (tool.tags && tool.tags.some(tag => tag.toLowerCase().includes(term)))
            );
        }
        this.renderToolsInColumns();
    }

    setupFilterTags() {
        const filterTags = document.getElementById('filterTags');
        const tags = ['AI', 'LLM', 'Coding', 'SaaS', 'API', 'Free', 'Paid', 'New'];
        
        tags.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'filter-tag';
            tagElement.textContent = tag;
            tagElement.addEventListener('click', () => {
                tagElement.classList.toggle('active');
                this.applyTagFilters();
            });
            filterTags.appendChild(tagElement);
        });
    }

    applyTagFilters() {
        const activeTags = Array.from(document.querySelectorAll('.filter-tag.active'))
            .map(tag => tag.textContent.toLowerCase());
        
        if (activeTags.length === 0) {
            this.filteredTools = [...this.tools];
        } else {
            this.filteredTools = this.tools.filter(tool => 
                tool.tags && tool.tags.some(tag => 
                    activeTags.includes(tag.toLowerCase())
                )
            );
        }
        this.renderToolsInColumns();
    }

    showSettings() {
        const modal = document.getElementById('settingsModal');
        const toggles = document.getElementById('columnToggles');
        
        toggles.innerHTML = '';
        this.columns.forEach(column => {
            const toggleDiv = document.createElement('div');
            toggleDiv.className = 'column-toggle';
            toggleDiv.innerHTML = `
                <input type="checkbox" id="toggle-${column.id}" ${column.visible ? 'checked' : ''}>
                <label for="toggle-${column.id}">${column.title}</label>
            `;
            
            const checkbox = toggleDiv.querySelector('input');
            checkbox.addEventListener('change', (e) => {
                column.visible = e.target.checked;
                this.saveSettings();
                this.renderColumns();
                this.renderToolsInColumns();
            });
            
            toggles.appendChild(toggleDiv);
        });
        
        modal.classList.add('show');
    }

    hideSettings() {
        document.getElementById('settingsModal').classList.remove('show');
    }

    showLoading(show) {
        const loading = document.getElementById('loadingIndicator');
        if (show) {
            loading.classList.add('show');
        } else {
            loading.classList.remove('show');
        }
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#6366f1'};
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            z-index: 1001;
            font-size: 14px;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize the dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AIToolsDashboard();
});
