/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --secondary-color: #f8fafc;
    --accent-color: #fbbf24;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --background: #ffffff;
    --background-secondary: #f8fafc;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--background-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

/* Cookie Consent */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--background);
    border-top: 1px solid var(--border-color);
    padding: 16px;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cookie-consent.show {
    transform: translateY(0);
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.cookie-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.btn-accept {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--radius-sm);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-accept:hover {
    background: var(--primary-hover);
}

.privacy-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 13px;
}

.privacy-link:hover {
    color: var(--primary-color);
}

/* Header */
.header {
    background: var(--background);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    flex-shrink: 0;
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.controls-section {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
}

.control-group label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
}

.control-select,
.control-input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 13px;
    background: var(--background);
    color: var(--text-primary);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.control-select:focus,
.control-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.control-select:disabled,
.control-input:disabled {
    background: var(--background-secondary);
    color: var(--text-muted);
    cursor: not-allowed;
}

.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: var(--radius-sm);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
    margin-top: 18px;
}

.search-btn:hover:not(:disabled) {
    background: var(--primary-hover);
}

.search-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
}

/* Search and Filter Bar */
.search-filter-bar {
    padding: 16px 24px;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: 16px;
    max-width: 1400px;
    margin: 0 auto;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 36px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    background: var(--background);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.filter-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-tag {
    background: var(--background);
    border: 1px solid var(--border-color);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
}

.filter-tag:hover,
.filter-tag.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.settings-btn {
    background: var(--background);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    font-size: 13px;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.settings-btn:hover {
    background: var(--background-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: var(--text-muted);
    border-radius: var(--radius-sm);
    transition: all 0.2s;
}

.close-btn:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.column-toggles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.column-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: var(--radius-sm);
    transition: background-color 0.2s;
}

.column-toggle:hover {
    background: var(--background-secondary);
}

.column-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.column-toggle label {
    font-size: 13px;
    font-weight: 400;
    margin: 0;
    cursor: pointer;
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-indicator.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-indicator p {
    color: var(--text-secondary);
    font-size: 14px;
}

/* Dashboard */
.dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    gap: 20px;
    overflow-x: auto;
    min-height: calc(100vh - 200px);
}

.dashboard-column {
    flex: 0 0 300px;
    background: var(--background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 250px);
}

.column-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-light);
    background: var(--background-secondary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.column-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.column-count {
    background: var(--primary-color);
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.column-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.tool-item {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-light);
    transition: background-color 0.2s;
    cursor: pointer;
}

.tool-item:hover {
    background: var(--background-secondary);
}

.tool-item:last-child {
    border-bottom: none;
}

.tool-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
}

.tool-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 16px;
    flex-shrink: 0;
}

.tool-info {
    flex: 1;
    min-width: 0;
}

.tool-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.4;
}

.tool-description {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.tool-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    margin-top: 8px;
    display: inline-block;
}

.tool-link:hover {
    text-decoration: underline;
}

.tool-tags {
    display: flex;
    gap: 4px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.tool-tag {
    background: var(--background-secondary);
    color: var(--text-muted);
    font-size: 11px;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-light);
}

/* Footer */
.footer {
    background: var(--background);
    border-top: 1px solid var(--border-color);
    padding: 24px;
    margin-top: auto;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
}

.attribution {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
}

.attribution p {
    font-size: 12px;
    color: var(--text-muted);
}

.attribution a {
    color: var(--primary-color);
    text-decoration: none;
}

.attribution a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .controls-section {
        justify-content: center;
    }
    
    .search-filter-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .dashboard {
        padding: 16px;
        gap: 16px;
    }
    
    .dashboard-column {
        flex: 0 0 280px;
    }
    
    .attribution {
        flex-direction: column;
        gap: 8px;
    }
}

/* Scrollbar Styling */
.column-content::-webkit-scrollbar {
    width: 6px;
}

.column-content::-webkit-scrollbar-track {
    background: var(--background-secondary);
}

.column-content::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.column-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
